/* pages/user/user.wxss */

/* 树叶飘落动画样式 */
page {
  background-color: #f4f9f4; /* 更柔和的浅绿色背景，与home页面一致 */
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  height: 100%;
}

/* 页面容器，使用flex布局 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  z-index: 20;
  padding-bottom: env(safe-area-inset-bottom); /* 适配不同设备底部安全区域 */
  padding-top: 180rpx; /* 为导航栏预留空间 */
}

/* 可滚动区域 */
.scrollable-content {
  flex: 1;
  height: 0;
  overflow: hidden;
  position: relative;
  z-index: 25;
  padding-bottom: env(safe-area-inset-bottom); /* 确保滚动内容有底部安全区域 */
}

/* 确保下拉不会因为overshoot效果导致看到背景 */
.scrollable-content::after {
  content: '';
  display: block;
  height: 40rpx;
}

.leaves-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  /* z-index由JS动态控制 */
  overflow: hidden;
  transition: opacity 0.3s ease;
  /* 添加过渡效果 */
}

.leaf {
  position: absolute;
  top: -150rpx;
  /* 让叶子从更高处开始，确保不在屏幕内滞空 */
  left: calc(var(--start-pos));
  width: var(--leaf-size);
  height: var(--leaf-size);
  opacity: 0.9;
  /* 使用linear动画函数，让下落更加自然匀速 */
  animation: leafFall var(--fall-duration) linear var(--fall-delay) forwards;
  /* 继承容器的z-index */
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
  will-change: transform, top;
  /* 优化性能 */
}

.leaf-image {
  width: 100%;
  height: 100%;
  animation: leafSpin 6s linear infinite;
}

@keyframes leafSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes leafFall {
  0% {
    top: -150rpx;
    transform: translateX(0) rotate(0deg) scale(1);
  }

  25% {
    top: calc(25vh);
    transform: translateX(100rpx) rotate(90deg) scale(0.95);
  }

  50% {
    top: calc(50vh);
    transform: translateX(-80rpx) rotate(180deg) scale(1);
  }

  75% {
    top: calc(75vh);
    transform: translateX(60rpx) rotate(270deg) scale(0.95);
  }

  100% {
    top: 120vh;
    transform: translateX(-60rpx) rotate(360deg) scale(1);
  }
}


/* 自定义导航栏样式 - 更新为与home页面一致的风格 */
.custom-nav {
  background: linear-gradient(to bottom, #43a047, rgba(165, 214, 167, 0.12)) !important; /* 中绿色到透明淡绿色渐变，与home一致 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
  margin-bottom: 0 !important;
  z-index: 100 !important;
  border-bottom: none !important;
  padding-bottom: 0 !important;
  height: auto !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
}

/* 添加伪元素 - 已禁用模糊效果 */
.custom-nav::after {
  display: none; /* 移除模糊过渡效果 */
}

/* 内部元素样式 */
.custom-nav .weui-navigation-bar__inner {
  background: transparent !important;
  backdrop-filter: none !important; /* 确保没有模糊效果 */
  -webkit-backdrop-filter: none !important; /* 确保没有模糊效果 */
  padding-bottom: 0 !important;
}

.custom-nav .weui-navigation-bar__center {
  color: #ffffff !important; /* 白色标题，与home一致 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  letter-spacing: 0.5px;
  font-weight: bold;
}

.custom-nav .weui-navigation-bar__btn_goback_wrapper {
  transition: all 0.3s ease;
}

.custom-nav .weui-navigation-bar__btn_goback_wrapper.weui-active {
  opacity: 0.7;
  transform: scale(0.95);
}


/* 头部样式 */
.header {
  background-color: #ffffff;
  padding: 30rpx 30rpx 20rpx;
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 20rpx;
  position: relative; /* 改为相对定位，不再固定 */
  z-index: 5; /* 降低z-index，避免遮挡其他元素 */
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100" fill="none"><path d="M85.355 14.645C79.825 9.118 72.712 5.2 65 3.2C57.288 1.2 49.175 1.2 41.465 3.2C33.753 5.2 26.64 9.118 21.111 14.645C15.583 20.173 11.667 27.288 9.667 35C7.667 42.712 7.667 50.825 9.667 58.535C11.667 66.247 15.585 73.36 21.111 78.889C26.638 84.417 33.753 88.333 41.465 90.333C49.177 92.333 57.29 92.333 65 90.333C72.71 88.333 79.827 84.415 85.355 78.889C90.882 73.362 94.8 66.247 96.8 58.535C98.8 50.823 98.8 42.71 96.8 35C94.8 27.29 90.882 20.173 85.355 14.645Z" fill="%2375f1ad" fill-opacity="0.05"/></svg>');
  background-repeat: no-repeat;
  background-position: right top;
  background-size: 200rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.15);
  margin-top: 10rpx; /* 添加顶部间距 */
}

.header-content {
  position: relative;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #43a047, #81c784);
  padding: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(43, 164, 113, 0.2);
}

/* 添加装饰气泡背景 */
.header-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.15) 5%, transparent 10%),
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.1) 7%, transparent 14%);
  background-size: 200rpx 200rpx, 250rpx 250rpx;
  opacity: 0.8;
  z-index: 0;
  pointer-events: none;
}

/* 编辑图标样式 */
.edit-icon {
  position: absolute;
  top: 38%;
  right: 6%;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  padding: 0 20rpx;
  height: 64rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
  transition: all 0.3s ease;
}

/* 编辑文字样式 */
.edit-text {
  font-size: 26rpx;
  color: #2ba471;
  margin-right: 8rpx;
  font-weight: 500;
}

.edit-icon:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.8);
}

.user-profile {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.avatar-container {
  position: relative;
  margin-right: 30rpx;
}



/* 成员徽章样式 */
.member-badge {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #ff9500, #ffb700);  /* 默认背景色 */
  color: white;
  font-size: 18rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: bold;
  white-space: nowrap;
  border: 2rpx solid rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 1rpx;
  z-index: 10;
  overflow: hidden;
  min-width: 40rpx;
  min-height: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: badgePulse 2s infinite;
  box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.4);
}

.member-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), transparent);
  border-radius: 10rpx 10rpx 0 0;
}

.member-badge-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.member-prefix {
  font-size: 16rpx;
  font-weight: bold;
  margin-right: 2rpx;
  opacity: 0.9;
}

.member-level {
  font-size: 18rpx;
  font-weight: bold;
}

/* 添加等级光晕效果 */
.level-halo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 130%;
  height: 130%;
  border-radius: 50%;
  z-index: 0;
  opacity: 0;
  pointer-events: none;
}

/* 为不同等级设置不同光晕颜色 */
.level-halo.level-1 {
  background: radial-gradient(circle, rgba(76, 175, 80, 0.3) 0%, rgba(76, 175, 80, 0) 70%);
  opacity: 0.5;
}

.level-halo.level-2 {
  background: radial-gradient(circle, rgba(33, 150, 243, 0.3) 0%, rgba(33, 150, 243, 0) 70%);
  opacity: 0.5;
}

.level-halo.level-3 {
  background: radial-gradient(circle, rgba(156, 39, 176, 0.3) 0%, rgba(156, 39, 176, 0) 70%);
  opacity: 0.5;
}

.level-halo.level-4 {
  background: radial-gradient(circle, rgba(255, 87, 34, 0.3) 0%, rgba(255, 87, 34, 0) 70%);
  opacity: 0.5;
}

.level-halo.level-5 {
  background: radial-gradient(circle, rgba(255, 193, 7, 0.3) 0%, rgba(255, 193, 7, 0) 70%);
  opacity: 0.6;
}

.level-halo.level-6 {
  background: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 215, 0, 0) 70%);
  opacity: 0.7;
  animation: goldenHaloPulse 2s infinite;
}

@keyframes goldenHaloPulse {
  0% {
    opacity: 0.5;
    width: 130%;
    height: 130%;
  }
  50% {
    opacity: 0.7;
    width: 140%;
    height: 140%;
  }
  100% {
    opacity: 0.5;
    width: 130%;
    height: 130%;
  }
}

@keyframes badgePulse {
  0% {
    transform: translateX(-50%) scale(1);
  }
  50% {
    transform: translateX(-50%) scale(1.05);
  }
  100% {
    transform: translateX(-50%) scale(1);
  }
}

/* 用户信息样式 */
.user-info {
  flex: 1;
  position: relative;
  z-index: 2;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 10rpx;
}

.user-name-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  /* 添加文本省略功能 */
  max-width: 280rpx; /* 限制最大宽度，为编辑按钮留出空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 未登录时的提示文字样式 */
.login-prompt-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  /* 不添加省略功能，让提示文字完整显示 */
}

.user-meta {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 6rpx;
  display: flex;
  flex-direction: column;
}

.user-phone {
  margin-top: 6rpx;
  color: rgba(255, 255, 255, 0.85);
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.user-phone .phone-number {
  color: #ffffff;
  font-size: 30rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  margin: 0;
  margin-left: -2%;
  padding: 0;
  background: none;
  border-radius: 0;
  width: auto;
}

/* 用户等级信息样式 */
.user-level-info {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  font-size: 24rpx;
}

.user-level-prefix {
  color: rgba(255, 255, 255, 0.9);
  margin-right: 6rpx;
}

.user-level-value {
  font-weight: bold;
  margin-right: 6rpx;
  color: #fff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.user-level-name {
  font-size: 22rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 用户经验条样式 */
.user-exp-info {
  display: flex;
  flex-direction: column;
  margin-top: 12rpx;
  width: 100%;
}

/* 调整用户信息中的进度条 */
.user-exp-info .t-progress {
  --td-progress-track-height: 12rpx;
  margin-top: 4rpx;
  height: 24rpx;
}

.exp-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
}

.exp-prefix {
  font-size: 22rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.exp-value {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 手机号绑定按钮 */
.phone-bind-container {
  margin-top: 16rpx;
}

.phone-bind-btn {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #ffffff;
  font-size: 24rpx;
  padding: 6rpx 18rpx;
  border-radius: 24rpx;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.phone-bind-btn::after {
  border: none;
}

.phone-bind-btn text {
  margin-left: 8rpx;
}

/* 已绑定手机号显示 */
.phone-number {
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #2ba471;
  display: flex;
  align-items: center;
  background: rgba(43, 164, 113, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  width: fit-content;
}

.phone-number text {
  margin-left: 8rpx;
}

/* 数据统计栏样式 */
.stats-container {
  display: flex;
  justify-content: space-between;
  padding: 30rpx 20rpx 20rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 248, 248, 0.8));
  border-radius: 16rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* 添加title-slogan作为背景 */
.stats-container::after {
  content: attr(data-slogan);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32rpx;
  color: rgba(67, 160, 71, 0.15);
  text-align: center;
  font-weight: 600;
  font-style: italic;
  letter-spacing: 2rpx;
  white-space: nowrap;
  z-index: 0;
  pointer-events: none;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  position: relative;
  z-index: 1;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #43a047;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
  position: relative;
  padding-bottom: 6rpx;
}

.stat-label::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20rpx;
  height: 2rpx;
  background-color: rgba(67, 160, 71, 0.3);
  border-radius: 1rpx;
}

/* 宣传口号样式 - 隐藏原来的显示位置 */
.title-slogan {
  display: none; /* 隐藏原来的显示位置 */
  font-size: 26rpx;
  color: #43a047;
  text-align: center;
  font-weight: 400;
  padding: 20rpx 0 10rpx;
  font-style: italic;
  letter-spacing: 2rpx;
  opacity: 0.85;
  text-shadow: 0 1rpx 3rpx rgba(67, 160, 71, 0.1);
  animation: sloganFadeIn 1.2s ease-out forwards;
}

@keyframes sloganFadeIn {
  0% { 
    opacity: 0;
    transform: scale(0.96);
  }
  100% { 
    opacity: 0.85;
    transform: scale(1);
  }
}

/* 签到项目样式 */
.sign-in-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 25%;
  position: relative;
  z-index: 1;
}

/* 确保签到按钮在统计栏中垂直对齐 */
.sign-in-item .check-in-container-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 调整签到按钮的垂直位置，使其与数字对齐 */
.sign-in-item .check-in-button-stats {
  margin-bottom: 8rpx;
}

/* 内容区域样式 */
.content-area {
  padding: 30rpx;
  padding-bottom: 60rpx; /* 增加底部内边距 */
  margin-bottom: 20rpx; /* 减小底部margin */
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 添加白玻璃效果，参考商品页面 */
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  
  /* 内容区域淡入动画初始状态 */
  opacity: 0;
  transform: translateY(30rpx);
  transition: opacity 0s, transform 0s;
}

/* 内容区域激活动画效果 */
.content-area-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

/* 添加绿色气泡背景装饰，参考商品页面 */
.content-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0; /* 调整z-index确保在背景上但在内容下 */
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(117, 241, 173, 0.1) 0%, rgba(117, 241, 173, 0.1) 4%, transparent 5%), 
    radial-gradient(circle at 85% 40%, rgba(43, 164, 113, 0.08) 0%, rgba(43, 164, 113, 0.08) 3%, transparent 4%),
    radial-gradient(circle at 30% 75%, rgba(117, 241, 173, 0.06) 0%, rgba(117, 241, 173, 0.06) 5%, transparent 6%),
    radial-gradient(circle at 70% 85%, rgba(43, 164, 113, 0.04) 0%, rgba(43, 164, 113, 0.04) 2%, transparent 3%),
    radial-gradient(circle at 60% 25%, rgba(117, 241, 173, 0.07) 0%, rgba(117, 241, 173, 0.07) 4%, transparent 5%),
    radial-gradient(circle at 20% 55%, rgba(43, 164, 113, 0.05) 0%, rgba(43, 164, 113, 0.05) 3%, transparent 4%);
  background-size: 300rpx 300rpx, 250rpx 250rpx, 220rpx 220rpx, 180rpx 180rpx, 230rpx 230rpx, 200rpx 200rpx;
  background-repeat: repeat;
  animation: subtle-float 40s linear infinite;
}

/* 添加气泡微妙浮动动画 */
@keyframes subtle-float {
  0% {
    background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
  }
  100% {
    background-position: 150rpx 150rpx, -120rpx 100rpx, 100rpx -80rpx, -90rpx -70rpx, 120rpx -60rpx, -80rpx 80rpx;
  }
}

/* 气泡漂浮动画 */
@keyframes bubbles-float {
  0% {
    background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%;
  }
  100% {
    background-position: 100rpx -50rpx, -80rpx 80rpx, 60rpx 60rpx, -40rpx -40rpx;
  }
}

/* 底部安全区域，防止内容被tabbar遮挡 */
.bottom-safe-area {
  height: 220rpx; /* 确保在各种设备上都有足够空间 */
  width: 100%;
  margin-bottom: 20rpx;
  padding-bottom: env(safe-area-inset-bottom); /* 适配不同设备底部安全区域 */
}

/* 卡片区块通用样式 */
.section-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
  border-left: 6rpx solid #43a047;
  animation: none; /* 禁用原来的动画 */
  opacity: 0;
  transform: translateY(20rpx);
  z-index: 2; /* 确保卡片在气泡上层 */
}

/* 内容区域激活时，卡片依次显示动画 */
.content-area-active .section-card {
  animation: card-fade-in 0.5s ease-out forwards;
}

/* 卡片依次显示，错开动画时间 */
.content-area-active .section-card:nth-child(1) { animation-delay: 0.1s; }
.content-area-active .section-card:nth-child(2) { animation-delay: 0.2s; }
.content-area-active .section-card:nth-child(3) { animation-delay: 0.3s; }
.content-area-active .section-card:nth-child(4) { animation-delay: 0.4s; }
.content-area-active .section-card:nth-child(5) { animation-delay: 0.5s; }

/* 卡片淡入动画 */
@keyframes card-fade-in {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background: linear-gradient(to bottom, #43a047, #66bb6a);
  border-radius: 4rpx;
}

/* 订单图标区域 */
.order-icons {
  display: flex;
  justify-content: flex-start;
  padding: 20rpx 0;
  flex-wrap: wrap;
  gap: 0; /* 移除gap，使用margin控制间距 */
}

/* 客服中心图标区域 - 左对齐自然排序 */
.service-icons {
  display: flex;
  justify-content: flex-start;
  padding: 20rpx 0;
  flex-wrap: wrap;
}

.service-icons .order-icon-item {
  margin-right: 20rpx;
}

.order-icons .order-icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 23%;
  margin-bottom: 20rpx;
  margin-right: 1.5%; /* 添加右边距，保持4个图标一行时的间距一致 */
}

/* 每行最后一个图标不需要右边距 */
.order-icons .order-icon-item:nth-child(4n) {
  margin-right: 0;
}

.order-icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 23%;
  margin-bottom: 20rpx;
}

/* 管理员按钮样式 */
.admin-item .admin-icon {
  background: linear-gradient(135deg, #e34d59, #f87f89);
  box-shadow: 0 4rpx 10rpx rgba(227, 77, 89, 0.2);
  border: 1rpx solid rgba(227, 77, 89, 0.3);
}

.admin-item .admin-text {
  color: #e34d59;
  font-weight: 500;
}

.admin-item .admin-icon:active {
  transform: scale(0.9);
  background: linear-gradient(135deg, #e34d59, #f87f89);
  box-shadow: 0 2rpx 8rpx rgba(227, 77, 89, 0.3);
}

.icon-wrapper {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(117, 241, 173, 0.15), rgba(67, 160, 71, 0.08));
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
  transition: all 0.3s;
  box-shadow: 0 4rpx 10rpx rgba(67, 160, 71, 0.1);
  border: 1rpx solid rgba(117, 241, 173, 0.3);
  position: relative;
  overflow: visible; /* 改为 visible，让红点可以显示 */
}

/* 红点样式 */
.red-dot {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 18rpx;
  height: 18rpx;
  background-color: #ff4444;
  border-radius: 50%;
  border: 2rpx solid #ffffff;
  z-index: 1000;
  box-shadow: 0 2rpx 4rpx rgba(255, 68, 68, 0.3);
}

/* 添加图标内部装饰 */
.icon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: -50%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-25deg);
  transition: all 0.5s ease;
}

.order-icon-item:hover .icon-wrapper::before {
  left: 150%;
}

.order-icon-item text {
  font-size: 24rpx;
  color: #666666;
  margin-top: 8rpx;
}

/* 动画效果 */
.icon-wrapper:active {
  transform: scale(0.9);
  background: linear-gradient(135deg, rgba(117, 241, 173, 0.25), rgba(67, 160, 71, 0.15));
  box-shadow: 0 2rpx 8rpx rgba(43, 164, 113, 0.15);
}

/* 养护小贴士 */
.tips-box {
  background: linear-gradient(145deg, rgba(117, 241, 173, 0.08), rgba(67, 160, 71, 0.05));
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 15rpx rgba(43, 164, 113, 0.08);
  border: 1rpx solid rgba(117, 241, 173, 0.2);
}

/* 添加装饰图案 */
.tips-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%2375f1ad" stroke-width="1" opacity="0.05"><path d="M12 22c4.97 0 9-4.03 9-9-4.97 0-9 4.03-9 9zm0-18c-4.97 0-9 4.03-9 9 4.97 0 9-4.03 9-9zm0 0c0 4.97 4.03 9 9 9-4.97 0-9-4.03-9-9zm0 0c0-4.97-4.03-9-9-9 4.97 0 9 4.03 9 9z"/></svg>');
  background-repeat: repeat;
  background-size: 60rpx 60rpx;
  opacity: 0.2;
  z-index: 0;
  animation: subtle-rotate 60s linear infinite;
}

@keyframes subtle-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.tip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}

/* 添加标题前的装饰图标 */
.tip-title::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #43a047, #66bb6a);
  margin-right: 12rpx;
  border-radius: 4rpx;
}

.tip-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  text-shadow: 0 1rpx 1rpx rgba(255, 255, 255, 0.8);
}

/* 星期几标签样式 */
.tip-weekday {
  font-size: 24rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #43a047, #66bb6a);
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  font-weight: 500;
  box-shadow: 0 3rpx 8rpx rgba(43, 164, 113, 0.25);
  position: relative;
  overflow: hidden;
  letter-spacing: 1rpx;
}

/* 添加高光效果 */
.tip-weekday::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-25deg);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% {
    left: 150%;
  }
}

/* 美化小贴士图标 */
.tip-icon {
  background: linear-gradient(145deg, rgba(117, 241, 173, 0.15), rgba(67, 160, 71, 0.08));
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(43, 164, 113, 0.15);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(117, 241, 173, 0.3);
}

/* 添加图标内部装饰 */
.tip-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8) 0%, transparent 20%);
  opacity: 0.6;
}

.tip-icon:active {
  transform: scale(0.95);
  background: linear-gradient(145deg, rgba(117, 241, 173, 0.2), rgba(67, 160, 71, 0.1));
}

/* 经验值卡片样式 */
.exp-content {
  padding: 20rpx 0;
  position: relative;
}

/* 添加装饰背景 */
.exp-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 80% 20%, rgba(76, 175, 80, 0.06) 0%, rgba(76, 175, 80, 0.06) 3%, transparent 6%),
    radial-gradient(circle at 20% 80%, rgba(76, 175, 80, 0.04) 0%, rgba(76, 175, 80, 0.04) 4%, transparent 8%);
  background-size: 150rpx 150rpx, 120rpx 120rpx;
  pointer-events: none;
  z-index: 0;
  opacity: 0.8;
}

.exp-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 18rpx;
  position: relative;
  z-index: 1;
}

.exp-level {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  background: rgba(67, 160, 71, 0.08);
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
}

.exp-value {
  font-size: 26rpx;
  color: #666;
  background: rgba(255, 193, 7, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.exp-progress-container {
  position: relative;
  margin-bottom: 30rpx;
  z-index: 1;
}

/* TDesign进度条样式定制 */
.exp-progress-container .t-progress {
  --td-progress-track-height: 16rpx;
  --td-progress-track-color: rgba(43, 164, 113, 0.1);
}

/* 为6级添加闪烁效果 */
.exp-progress-container .level-6 .t-progress__bar {
  animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
  0% {
    box-shadow: 0 0 3rpx rgba(255, 215, 0, 0.5);
  }
  50% {
    box-shadow: 0 0 8rpx rgba(255, 215, 0, 0.8);
  }
  100% {
    box-shadow: 0 0 3rpx rgba(255, 215, 0, 0.5);
  }
}

/* 最高等级提示 */
.exp-max-level {
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.exp-max-text {
  color: #ff9800;
  font-size: 28rpx;
  text-align: center;
  font-weight: bold;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 30rpx;
  padding: 12rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.2);
  border: 1rpx solid rgba(255, 193, 7, 0.3);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* 添加闪光效果 */
.exp-max-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: skewX(-25deg);
  animation: shimmer 3s infinite;
}

/* 退出登录区域样式 */
.logout-section {
  margin: 30rpx 0 60rpx; /* 增加底部边距 */
  padding: 0 30rpx;
}

/* 为退出登录按钮添加渐变和阴影效果 */
.logout-section .t-button--danger {
  background: linear-gradient(135deg, #f44336, #e53935);
  box-shadow: 0 4rpx 12rpx rgba(244, 67, 54, 0.2);
  border: none;
  position: relative;
  overflow: hidden;
}

/* 添加按钮内部闪光效果 */
.logout-section .t-button--danger::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: skewX(-25deg);
  transition: all 0.5s ease;
}

.logout-section .t-button--danger:active::before {
  left: 150%;
}

/* 编辑个人资料模态框样式 */
.edit-profile-modal {
  background: linear-gradient(145deg, #ffffff, #f8f8f8);
  border-radius: 24rpx;
  width: 650rpx;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.edit-profile-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 90% 10%, rgba(67, 160, 71, 0.08) 0%, rgba(67, 160, 71, 0.08) 2%, transparent 3%),
    radial-gradient(circle at 10% 90%, rgba(67, 160, 71, 0.06) 0%, rgba(67, 160, 71, 0.06) 3%, transparent 4%);
  background-size: 150rpx 150rpx, 120rpx 120rpx;
  pointer-events: none;
  z-index: 0;
  opacity: 0.5;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}

.modal-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 36rpx;
  background: linear-gradient(to bottom, #43a047, #66bb6a);
  border-radius: 4rpx;
}

.modal-body {
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.nickname-area, .phone-area {
  margin-bottom: 30rpx;
}

.nickname-header, .phone-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.nickname-label, .phone-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.nickname-required {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-left: 10rpx;
}

.phone-number-display {
  font-size: 28rpx;
  color: #43a047;
  margin-left: 16rpx;
  font-weight: 500;
}

.nickname-tips, .phone-tips {
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
  padding-left: 12rpx;
}

.phone-tips.error {
  color: #ff4d4f;
}

.modal-footer {
  display: flex;
  padding: 20rpx 0 10rpx;
  position: relative;
  z-index: 1;
}

.cancel-btn, .save-btn {
  flex: 1;
}

.cancel-btn {
  margin-right: 20rpx;
}

.modal-footer .t-button {
  border: none !important;
  background-color: #43a047 !important;
  color: white !important;
  box-shadow: 0 4rpx 10rpx rgba(67, 160, 71, 0.2) !important;
}

.modal-footer .t-button::after {
  border: none !important;
}

.modal-footer .t-button--outline {
  border: none !important;
}

/* 禁用状态的按钮 */
.modal-footer .t-button--disabled {
  background-color: rgba(67, 160, 71, 0.4) !important;
  color: rgba(255, 255, 255, 0.6) !important;
}

/* 确保内部元素没有边框 */
.modal-footer .t-button__content {
  border: none !important;
}

.modal-footer .t-button--size-large {
  border: none !important;
}

.modal-footer .t-button--theme-light,
.modal-footer .t-button--theme-primary {
  border: none !important;
}

/* 强制覆盖所有可能的边框样式 */
.modal-footer button {
  border: none !important;
  outline: none !important;
}

/* 微信头像选择器样式 */
.wx-avatar-chooser {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar-wrapper {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  overflow: hidden;
  padding: 0;
  margin: 0;
  background: none;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.avatar-wrapper::after {
  border: none;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-edit-icon {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60rpx;
  background: linear-gradient(to bottom, rgba(67, 160, 71, 0.8), rgba(67, 160, 71, 0.9));
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s;
}

.avatar-wrapper:active .avatar-edit-icon {
  background: linear-gradient(to bottom, rgba(67, 160, 71, 0.9), rgba(67, 160, 71, 1));
}

.avatar-tip {
  font-weight: bolder;

  font-size: 35rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 微信昵称输入框样式 */
.nickname-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f8f8;
  transition: all 0.3s;
}

.nickname-input:focus {
  border-color: #43a047;
  background-color: #ffffff;
  box-shadow: 0 0 0 2rpx rgba(67, 160, 71, 0.2);
}

/* 手机号输入容器样式 */
.phone-input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 10rpx;
}

/* 垂直排列的手机号输入容器 */
.phone-input-stacked {
  flex-direction: column;
  align-items: stretch;
  gap: 16rpx;
}

.phone-input {
  flex: 1;
}

/* 全宽按钮样式 */
.get-phone-btn-full {
  width: 100%;
  font-weight: bold;
  font-size: 30rpx;
}

.get-phone-btn {
  min-width: 160rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
  background: linear-gradient(135deg, #43a047, #66bb6a);
  color: #fff;
  border-radius: 8rpx;
  text-align: center;
  margin: 0;
  box-shadow: 0 4rpx 10rpx rgba(67, 160, 71, 0.2);
}

.get-phone-btn::after {
  border: none;
}

/* 为6级添加闪烁效果 */
.exp-progress-container .t-progress__info.level-6 {
  animation: textGlow 2s infinite;
}

@keyframes textGlow {
  0% {
    text-shadow: 0 1rpx 2rpx rgba(255, 152, 0, 0.4);
  }
  50% {
    text-shadow: 0 1rpx 4rpx rgba(255, 152, 0, 0.7);
  }
  100% {
    text-shadow: 0 1rpx 2rpx rgba(255, 152, 0, 0.4);
  }
}

/* 默认头像样式 */
.default-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(145deg, #f0f0f0, #fafafa);
  border-radius: 50%;
}

/* 关于我们弹窗样式 */
.about-us-modal {
  width: 650rpx;
  max-height: 78vh;
  background-color: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.about-us-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.about-us-modal .modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.about-us-modal .modal-body {
  padding: 30rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-height: 60vh; /* 设置最大高度，确保内容可滚动 */
}

.about-us-logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.about-us-logo image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

.about-us-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #2ba471;
  margin-bottom: 10rpx;
  text-align: center;
}

.about-us-slogan {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 30rpx;
  font-style: italic;
  text-align: center;
}

.about-us-description {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 30rpx;
  text-align: justify;
  padding: 0 20rpx;
}

.about-us-features {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: rgba(43, 164, 113, 0.05);
  border-radius: 12rpx;
  border-left: 6rpx solid #2ba471;
}

.feature-icon {
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(43, 164, 113, 0.1);
  border-radius: 50%;
}

.feature-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.feature-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 6rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666666;
}

.about-us-contact {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx dashed rgba(0, 0, 0, 0.1);
}

.about-us-contact text {
  font-size: 24rpx;
  color: #666666;
}


.about-us-modal .modal-footer {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  justify-content: center;
}

.about-us-modal .confirm-btn {
  width: 100%;
}

/* 充值弹窗样式 */
.recharge-modal {
  width: 650rpx;
  max-height: 80vh;
  background-color: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 充值弹窗装饰背景 */
.recharge-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 90% 10%, rgba(67, 160, 71, 0.08) 0%, rgba(67, 160, 71, 0.08) 2%, transparent 3%),
    radial-gradient(circle at 10% 90%, rgba(117, 241, 173, 0.06) 0%, rgba(117, 241, 173, 0.06) 3%, transparent 4%);
  background-size: 150rpx 150rpx, 120rpx 120rpx;
  pointer-events: none;
  z-index: 0;
  opacity: 0.5;
}

.recharge-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.recharge-modal .modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}

.recharge-modal .modal-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 36rpx;
  background: linear-gradient(to bottom, #43a047, #66bb6a);
  border-radius: 4rpx;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.modal-close:active {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

.recharge-modal .modal-body {
  padding: 30rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  flex: 1;
  position: relative;
  z-index: 1;
}

/* 当前余额显示 */
.current-balance {
  background: linear-gradient(135deg, rgba(67, 160, 71, 0.1), rgba(117, 241, 173, 0.08));
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  text-align: center;
  border: 1rpx solid rgba(67, 160, 71, 0.2);
  position: relative;
  overflow: hidden;
}

.current-balance::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8) 0%, transparent 20%);
  opacity: 0.6;
  pointer-events: none;
}

.balance-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
  position: relative;
  z-index: 1;
}

.balance-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #43a047;
  text-shadow: 0 2rpx 4rpx rgba(67, 160, 71, 0.2);
  position: relative;
  z-index: 1;
}

/* 充值选项 */
.recharge-options {
  margin-bottom: 30rpx;
}

.recharge-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.recharge-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background: linear-gradient(to bottom, #43a047, #66bb6a);
  border-radius: 3rpx;
}

.recharge-amounts {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 15rpx;
}

.amount-item {
  background-color: #ffffff;
  border: 2rpx solid rgba(67, 160, 71, 0.2);
  border-radius: 12rpx;
  padding: 20rpx 15rpx;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.amount-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(67, 160, 71, 0.1), transparent);
  transform: skewX(-25deg);
  transition: all 0.5s ease;
}

.amount-item:active::before {
  left: 150%;
}

.amount-item.selected {
  border-color: #43a047;
  background: linear-gradient(135deg, rgba(67, 160, 71, 0.1), rgba(117, 241, 173, 0.08));
  box-shadow: 0 4rpx 15rpx rgba(67, 160, 71, 0.2);
  transform: translateY(-2rpx);
}

.amount-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.amount-item.selected .amount-value {
  color: #43a047;
}

.amount-points {
  font-size: 24rpx;
  color: #666666;
}

.amount-item.selected .amount-points {
  color: #43a047;
  font-weight: 500;
}

/* 测试标签样式 */
.test-label {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(255, 152, 0, 0.3);
  z-index: 2;
}

/* 充值说明 */
.recharge-notice {
  background-color: rgba(255, 193, 7, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 6rpx solid #ffc107;
}

.notice-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.notice-content text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.recharge-modal .modal-footer {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  gap: 20rpx;
  position: relative;
  z-index: 1;
}

.recharge-modal .cancel-btn,
.recharge-modal .confirm-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.recharge-modal .cancel-btn {
  background-color: #f5f5f5;
  color: #666666;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.recharge-modal .confirm-btn {
  background: linear-gradient(135deg, #43a047, #66bb6a);
  color: #ffffff;
  box-shadow: 0 4rpx 15rpx rgba(67, 160, 71, 0.3);
  border: none;
}

.recharge-modal .confirm-btn[disabled] {
  background: #cccccc;
  color: #999999;
  box-shadow: none;
}

/* 按钮点击效果 */
.recharge-modal .cancel-btn:active {
  background-color: #eeeeee;
  transform: scale(0.98);
}

.recharge-modal .confirm-btn:active:not([disabled]) {
  background: linear-gradient(135deg, #388e3c, #5a9f5d);
  transform: scale(0.98);
}


