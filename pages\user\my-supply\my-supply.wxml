<!-- pages/user/my-supply/my-supply.wxml -->
<view class="page-container">
  <!-- 导航栏组件 -->
  <view class="nav-container">
    <navigation-bar title="{{isQuoteMode ? pageTitle : '我的供应'}}" back="{{true}}" home="{{true}}" bind:back="onNavBack" bind:home="onNavHome" extClass="custom-nav"></navigation-bar>
  </view>
  
  <!-- 天气信息section -->
  <view class="weather-section" wx:if="{{isLogined}}">
    <view class="weather-tips-container">
      <!-- 天气提示 -->
      <view class="weather-tips">
        <text wx:if="{{!isQuoteMode}}">{{weatherData.tips || '今天天气不错，适合发布新的供应信息~'}}</text>
        <text wx:else style="color: #ff4500; font-weight: bold;">报价完成之后，3天之后自动删除请及时保存产看文件</text>
      </view>
    </view>
    <view class="weather-container">
      <!-- 头像部分 -->
      <view class="avatar-container" style="position: relative; left: 0rpx; top: 1rpx">
        <image class="avatar-image" src="{{userInfo.avatarUrl || '/images/user-avatar-default.png'}}" bindtap="onTapAvatar"></image>
        <!-- 添加注册时间显示 -->
        <view class="register-time">{{registerTimeText}}</view>
      </view>
      
      <view class="weather-info">
        <view class="weather-location">
          <t-icon name="location" size="32rpx" color="#ffffff"></t-icon>
          <text>{{weatherData.location || '正在定位...'}}</text>
        </view>
        <view class="weather-temp">
          <text class="temp-num">{{weatherData.temp || '--'}}</text>
          <text class="temp-unit">℃</text>
        </view>
        <view class="weather-desc">{{weatherData.text || '晴朗'}}</view>
      </view>
      <view class="weather-icon {{weatherAnimation ? 'animate' : ''}}">
        <t-icon wx:if="{{weatherData.tDesignIcon}}" name="{{weatherData.tDesignIcon}}" size="120rpx" color="#ffffff"></t-icon>
        <t-icon wx:else name="sunny" size="120rpx" color="#ffffff"></t-icon>
      </view>
    </view>
    <!-- 供应/报价数量信息容器 -->
    <view class="supply-info-container">
      <view class="supply-count"> 
        <t-icon name="{{isQuoteMode ? 'chart-bubble' : 'shop'}}" size="28rpx" color="#ffffff" style="padding-right: 10rpx;"></t-icon>
        <text style="">{{isQuoteMode ? '报价数量' : '产品数量'}}: {{total}}</text>
      </view>
      <view class="view-count" wx:if="{{!isQuoteMode}}">
        <t-icon name="browse" size="28rpx" color="#ffffff" style="padding-right: 10rpx;"  ></t-icon>
        <text style="">总浏览数: {{totalViews}}</text>
      </view>
    </view>
  </view>

  <!-- 主内容区 -->
  <view class="content-container">
    <!-- 搜索栏 - 新设计 -->
    <!-- <view class="search-container" wx:if="{{isLogined}}"> -->
      <!-- <view class="search-box">
        <t-icon name="search" size="48rpx" color="#888"></t-icon>
        <input class="search-input" placeholder="搜索供应信息" bindinput="onSearchInput" value="{{searchKeyword}}" confirm-type="search" bindconfirm="onSearch" />
        <view class="search-btn" bindtap="onSearch">搜索</view>
      </view> -->
    <!-- </view> -->

    <!-- 未登录状态 -->
    <view class="empty-state" wx:if="{{!isLogined}}">
      <t-icon name="info-circle" size="80rpx" color="#43a047"></t-icon>
      <text class="empty-text">请先登录后查看您的供应</text>
    </view>

    <!-- 供应列表 - 仅在已登录时显示 -->
    <view class="supply-list-container" wx:if="{{isLogined}}">
      <!-- 加载状态 -->
      <view class="loading-state" wx:if="{{loading}}">
        <view class="loading-icon"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!loading && supplyList.length === 0}}">
        <t-icon name="info-circle" size="80rpx" color="#43a047"></t-icon>
        <text class="empty-text">{{isQuoteMode ? '暂无报价信息' : '暂无供应信息'}}</text>
        <view class="empty-action" bindtap="goToPublish" wx:if="{{!isQuoteMode}}">去发布</view>
      </view>
      
      <!-- 供应列表（两列布局） -->
      <view class="supply-grid" wx:if="{{supplyList.length > 0 && !isQuoteMode}}">
        <block wx:for="{{supplyList}}" wx:key="_id">
          <view class="supply-item" bindtap="viewDetail" data-id="{{item._id}}">
            <!-- 供应内容 -->
            <view class="supply-content">
              <!-- 图片区域，优先使用服务器图片，无图时使用默认图 -->
              <view class="supply-image">
                <image src="{{item.imageList && item.imageList.length > 0 ? item.imageList[0] : '/images/organic.png'}}" mode="aspectFill"></image>
                <!-- #elif scaleToFill:完整显示(strange) aspectFit:完全显示(normal)  old:aspectFill -->
                <view class="status-tag" style="{{item.status === 'deactivated' ? 'background-color: #f44336;' : ''}}">
                  {{ (item.status === 'deactivated' ? '已过期不再展示,点击修改重拍' : '展示中')}}
                </view>
                <view class="photo-date">{{item.photoUpdated || '无拍照日期'}}</view>
                <!-- 添加照片张数显示 -->
                <view class="photo-count" wx:if="{{item.imageList && item.imageList.length > 0}}">
                
                  <text>{{item.imageList.length}}张</text>
                </view>
          
              </view>
              
              <!-- 信息 -->
              <view class="supply-info">
                <view class="title-price-row">
                  <view class="supply-title">{{item.title}}</view>
                  <view class="supply-price">{{item.formattedPrice}}</view>
                </view>
                <view class="supply-specs">
                  <text wx:if="{{item.height}}">高度:{{item.height}}cm</text>
                  <text wx:if="{{item.meter_diameter}}">米径:{{item.meter_diameter}}cm</text>
                </view>
                <view class="supply-quantity">供应数量: {{item.quantity || '未知'}}</view>
              </view>
            </view>
            
            <!-- 操作按钮 -->
            <view class="supply-actions">
             
              <view class="action-btn remove" catchtap="removeSupply" data-id="{{item._id}}">
                <t-icon name="close-circle" size="36rpx" color="red"></t-icon>
                <text style="font-size:30rpx ;color: #ff0000;font-weight: 900; ">删除</text>
              </view>
              <view class="action-btn update" catchtap="updateSupply" data-id="{{item._id}}">
                <t-icon name="edit" size="36rpx" color="#61b4f8"></t-icon>
                <text style="font-size: 30rpx;color: #61b4f8;  font-weight: 900; ">修改</text>
              </view>
            </view>
            
          </view>
        </block>
      </view>
      
      <!-- 报价列表  报价模式-->
      <view class="quote-list" wx:if="{{supplyList.length > 0 && isQuoteMode}}">
        <block wx:for="{{supplyList}}" wx:key="_id">
          <view class="quote-item" bindtap="viewDetail" data-id="{{item._id}}">
            <!-- 报价内容 -->
            <view class="quote-content">
              <!-- 项目名称和状态 -->
              <view class="quote-header">
                <view class="quote-title">{{item.projectName || '未命名项目'}}</view>
                <view class="quote-status {{item.statusClass}}">{{item.statusText}}</view>
              </view>
              
              <!-- 报价信息 -->
              <view class="quote-info">
                <view class="quote-row">
                  <t-icon name="time" size="28rpx" color="#666666" />
                  <text class="quote-label">提交时间:</text>
                  <text class="quote-value">{{item.createTimeStr}}</text>
                </view>
                <view class="quote-row">
                  <t-icon name="mobile" size="28rpx" color="#666666" />
                  <text class="quote-label">联系电话:</text>
                  <text class="quote-value">{{item.phoneNumber}}</text>
                </view>
                <view class="quote-row">
                  <t-icon name="image" size="28rpx" color="#666666" />
                  <text class="quote-label">表格图片:</text>
                  <text class="quote-value">{{item.imageList.length}}张</text>
                </view>
              </view>
              
              <!-- 缩略图预览 -->
              <view class="quote-images" wx:if="{{item.imageList && item.imageList.length > 0}}">
                <block wx:for="{{item.imageList}}" wx:for-item="imgUrl" wx:for-index="imgIndex" wx:key="imgIndex">
                  <image 
                    src="{{imgUrl}}" 
                    mode="aspectFill" 
                    class="quote-thumb" 
                    data-urls="{{item.imageList}}" 
                    data-current="{{imgIndex}}"
                    catchtap="previewQuoteImage"
                    wx:if="{{imgIndex < 3}}"
                  ></image>
                </block>
                <view class="more-images" wx:if="{{item.imageList.length > 3}}">+{{item.imageList.length - 3}}</view>
              </view>
            </view>
          </view>
        </block>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{!loading && hasMore && supplyList.length > 0}}">
        <text class="load-more-text" bindtap="loadMore">加载更多</text>
      </view>
      
      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!loading && !hasMore && supplyList.length > 0}}">
        <text class="no-more-text">没有更多了</text>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-bottom-area" style="height: {{safeBottomHeight}}rpx;"></view>
</view>

<!-- 自定义tabbar -->
<custom-tab-bar></custom-tab-bar> 