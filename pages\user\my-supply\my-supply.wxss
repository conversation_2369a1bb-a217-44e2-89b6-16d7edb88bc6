/* pages/user/my-supply/my-supply.wxss */
page {
  background-color: #f4f9f4;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 页面容器 - 使用flex布局 */
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* 导航容器 */
.nav-container {
  width: 100%;
  position: relative;
  z-index: 1000;
  flex-shrink: 0;
}

/* 主内容区 */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-bottom: 140rpx; /* 为tabbar预留空间 */
  box-sizing: border-box;
}

/* 自定义导航栏样式 */
.custom-nav {
  background: #43a047 !important;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
  z-index: 999 !important;
  position: relative !important;
  width: 100% !important;
}

.custom-nav::after {
  display: none;
}

.custom-nav .weui-navigation-bar__inner {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.custom-nav .weui-navigation-bar__center {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
  font-weight: bold;
}

.custom-nav .weui-navigation-bar__btn_goback_wrapper {
  transition: all 0.3s ease;
}

.custom-nav .weui-navigation-bar__btn_goback_wrapper.weui-active {
  opacity: 0.7;
  transform: scale(0.95);
}

/* 确保图标颜色为白色 */
.custom-nav .weui-navigation-bar__btn_goback {
  color: #ffffff !important;
}

.custom-nav .weui-navigation-bar__btn {
  color: #ffffff !important;
}

/* 天气section样式 */
.weather-section {
  width: 100%;
  background: linear-gradient(135deg, #43a047 0%, #2e7d32 100%);
  padding: 30rpx;
  box-sizing: border-box;
  color: #ffffff;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.weather-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='rgba(255, 255, 255, 0.1)' d='M0,192L48,186.7C96,181,192,171,288,170.7C384,171,480,181,576,186.7C672,192,768,192,864,170.7C960,149,1056,107,1152,106.7C1248,107,1344,149,1392,170.7L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  background-position: center;
  opacity: 0.5;
  z-index: 0;
}

.weather-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  z-index: 1;
  width: 100%;
  padding: 10rpx 0;
}

/* 添加头像容器样式 */
.avatar-container {
  position: relative;
  margin-right: 20rpx;
  flex-shrink: 0;
  z-index: 2;
  display: block;
  width: 40%;
  height: 40%;
 
  flex-direction: column;
  align-items: center;
 
  overflow: visible;
  padding-bottom: 5rpx; /* 为注册时间文本留出空间 */
}

/* 头像样式 - 使用普通image样式 */
.avatar-image {
  width: 180rpx;
  height: 180rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  border-radius: 50%; /* 圆形头像 */
  display: block;
  margin-bottom: 8rpx; /* 头像和注册时间之间的间距 */
}

/* 注册时间样式 */
.register-time {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  text-shadow: 0 1rpx 1rpx rgba(0, 0, 0, 0.2);
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10rpx;
  padding: 2rpx 2rpx;
  line-height: 1.2;
  max-width: 180rpx;
  max-height: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  transform: scale(0.9);
  transform-origin: center center;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.weather-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-left: 10rpx;
}

.weather-location {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.weather-location t-icon {
  margin-right: 8rpx;
}

.weather-temp {
  display: flex;
  align-items: flex-start;
}

.temp-num {
  font-size: 64rpx;
  font-weight: bold;
  line-height: 1;
}

.temp-unit {
  font-size: 32rpx;
  margin-top: 8rpx;
  margin-left: 4rpx;
}

.weather-desc {
  font-size: 28rpx;
  margin-top: 8rpx;
  opacity: 0.9;
}

.weather-icon {
  width: 120rpx;
  height: 120rpx;
  position: relative;
  margin-left: auto; /* 将天气图标推到右侧 */
}

.weather-icon image {
  width: 100%;
  height: 100%;
}

.weather-icon.animate {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* 天气提示容器 - 新增 */
.weather-tips-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rpx;
  position: relative;
  z-index: 1;
}

/* 供应信息容器 - 新增 */
.supply-info-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  z-index: 1;
  padding: 9rpx 0;
}

/* 供应数量样式 - 新增 */
.supply-count {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #fcf700;
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  margin-left: 3rpx;
}

/* 浏览量样式 - 新增 */
.view-count {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #fcf700;
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  margin-left: 20rpx;
}

.supply-count t-icon, .view-count t-icon {
  margin-right: 8rpx;
}

/* 天气提示样式 - 调整 */
.weather-tips {
  width: 100%;
  font-size: 26rpx;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  padding-left: 10rpx;
  border-left: 6rpx solid rgba(255, 255, 255, 0.5);
  text-align: right;
}

/* 搜索容器 - 新设计 */
.search-container {
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #43a047;
  flex-shrink: 0;
  display: flex;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 36rpx;
  padding: 0 20rpx;
  height: 72rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  width: 92%;
  margin-left: 0;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
  padding: 0 20rpx;
}

.search-btn {
  height: 56rpx;
  background-color: #43a047;
  color: #ffffff;
  font-size: 28rpx;
  padding: 0 30rpx;
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}

/* 供应列表容器 */
.supply-list-container {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
}

/* 两列网格布局 */
.supply-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 0 -8rpx;
}

.supply-item {
  width: calc(50% - 16rpx);
  margin: 0 8rpx 50rpx 8rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  transform: translateY(0);
  min-height: 460rpx;
}

.supply-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.12);
}

.supply-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 360rpx;
}

.supply-image {
  width: 100%;
  height: 260rpx;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  overflow: hidden;
}

.supply-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.1));
  z-index: 1;
}

.supply-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.supply-item:active .supply-image image {
  transform: scale(1.05);
}

.status-tag {
  position: absolute;
  top: 5rpx;
  right: 12rpx;
  padding: 6rpx 16rpx;
  font-size: 22rpx;
  color: #ffffff;
  background-color: #43a047;
  border-radius: 20rpx;
  z-index: 2;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.photo-date {
  position: absolute;
  bottom: 12rpx;
  left: 12rpx;
  padding: 6rpx 16rpx;
  font-size: 21rpx;
  color: #ffffff;
  background-color: #f44336; /* 红色背景 */
  border-radius: 20rpx;
  z-index: 2;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 照片张数显示 */
.photo-count {
  position: absolute;
  bottom: 12rpx;
  right: 12rpx;
  padding: 6rpx 16rpx;
  font-size: 21rpx;
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  z-index: 2;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
}


.supply-info {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  min-height: 140rpx;
}

/* 标题和价格的行布局 */
.title-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  width: 100%;
}

.supply-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  margin-right: 10rpx;
}

.supply-price {
  font-size: 27rpx;
  color: #f44336;
  font-weight: 600;
  white-space: nowrap;
}

.supply-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  background-color: #f9f9f9;
  padding: 10rpx 14rpx;
  border-radius: 8rpx;
}

.supply-quantity {
  font-size: 24rpx;
  color: #43a047;
  font-weight: 600;
  display: flex;
  align-items: center;
  margin-top: 12rpx;
}

/* 操作按钮样式 - 调整大小 */
.supply-actions {
  display: flex;
  padding: 10rpx 10rpx;
  background-color: #ffffff;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  margin-top: auto;
  min-height: 70rpx;
 
}

.action-btn {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 14rpx 0;
  font-size: 24rpx;
  color: #ffffff;
  border-radius: 15rpx;
  margin: 0 8rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  height: 40rpx;
  line-height: 1.2;
}

.action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.action-btn text {
  margin-left: 6rpx;
}

.action-btn.update {
  /* background: linear-gradient(135deg, #61b4f8, #4a8aca); */
}

.action-btn.remove {
  /* background: linear-gradient(135deg, #fd7147, #fd7147); */
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-icon {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid rgba(67, 160, 71, 0.2);
  border-top: 4rpx solid #43a047;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #888;
  font-weight: 500;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 16rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.empty-text {
  font-size: 30rpx;
  color: #888;
  margin-top: 20rpx;
  margin-bottom: 30rpx;
}

.empty-action {
  font-size: 28rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #43a047, #2e7d32);
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.2);
  transition: all 0.3s ease;
}

.empty-action:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(46, 125, 50, 0.1);
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #43a047;
  padding: 12rpx 30rpx;
  border: 1rpx solid #43a047;
  border-radius: 30rpx;
  background-color: rgba(67, 160, 71, 0.05);
  transition: all 0.3s ease;
  display: inline-block;
}

.load-more-text:active {
  background-color: rgba(67, 160, 71, 0.1);
  transform: scale(0.98);
}

/* 没有更多样式 */
.no-more {
  text-align: center;
  padding: 30rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
  position: relative;
  display: inline-block;
  padding: 0 30rpx;
}

.no-more-text::before,
.no-more-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 1rpx;
  background-color: #ddd;
}

.no-more-text::before {
  left: -60rpx;
}

.no-more-text::after {
  right: -60rpx;
}

/* 报价列表样式 */
.quote-list {
  width: 100%;
  padding: 20rpx 0;
}

.quote-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
}

.quote-content {
  padding: 24rpx;
}

.quote-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.quote-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.quote-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-left: 10rpx;
}

.status-pending {
  background-color: #faad14;
  color: #ffffff;
}

.status-responded {
  background-color: #52c41a;
  color: #ffffff;
}

.status-rejected {
  background-color: #ff4d4f;
  color: #ffffff;
}

.status-paid {
  background-color: #1890ff;
  color: #ffffff;
}

.status-completed {
  background-color: #43a047;
  color: #ffffff;
}

.quote-info {
  margin-bottom: 16rpx;
}

.quote-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #666666;
}

.quote-label {
  margin: 0 10rpx;
  color: #999999;
}

.quote-value {
  color: #333333;
}

.quote-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16rpx;
}

.quote-thumb {
  width: 160rpx;
  height: 160rpx;
  margin-right: 10rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.more-images {
  width: 160rpx;
  height: 160rpx;
  background-color: rgba(0, 0, 0, 0.05);
  color: #999999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border-radius: 8rpx;
}



/* 底部安全区域 */
.safe-bottom-area {
  width: 100%;
  min-height: 40rpx;
}
